version: '3.8'

services:
  bilibili-services:
    build: 
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
    container_name: bilibili-data-services
    ports:
      - "5001:5001"  # 冷启动格式化服务
      - "5003:5003"  # 播放量获取服务  
      - "5004:5004"  # BV号提取服务
    volumes:
      - ./data:/app/data
      - ./temp:/app/temp
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5004/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - bilibili-network

networks:
  bilibili-network:
    driver: bridge
