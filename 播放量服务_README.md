# 📊 播放量获取服务

一个基于Flask的Web服务，用于批量获取视频播放量数据。支持上传Excel文件，自动解析视频链接并获取播放量，然后生成包含播放量数据的新Excel文件。

## 🚀 功能特点

- **🌐 Web界面**: 友好的拖拽上传界面
- **📊 实时进度**: 显示处理进度和状态
- **🔄 异步处理**: 后台多线程处理，不阻塞界面
- **📱 响应式设计**: 支持手机和电脑访问
- **🔌 API接口**: 支持程序化调用
- **📁 多格式支持**: 支持.xlsx和.xls文件
- **📋 多工作表**: 支持Excel多个工作表处理

## 📦 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt
```

## 🎯 快速开始

### 方法1: 使用启动脚本（推荐）

```bash
python start_service.py
```

### 方法2: 直接启动

```bash
python 播放量_web.py
```

启动后访问: **http://localhost:5002**

## 🖥️ Web界面使用

### 步骤1: 上传Excel文件
- 点击上传区域选择文件
- 或直接拖拽Excel文件到上传区域
- 支持 `.xlsx` 和 `.xls` 格式

### 步骤2: 配置参数
- **选择工作表**: 从下拉列表中选择要处理的工作表
- **视频链接列**: 选择包含视频链接的列
- **播放量列名**: 输入要写入播放量的列名（如果不存在会自动创建）

### 步骤3: 处理和下载
- 点击"开始处理"按钮
- 实时查看处理进度
- 处理完成后下载结果文件

## 🔌 API接口使用

### 接口地址
```
POST /api/process
```

### 请求参数
- `file`: Excel文件（必需）
- `link_column`: 链接列名（默认: "链接"）
- `play_count_column`: 播放量列名（默认: "播放量"）
- `sheet_name`: 工作表名或索引（默认: 0）

### 使用示例

#### cURL命令
```bash
curl -X POST \
  -F "file=@your_file.xlsx" \
  -F "link_column=视频链接" \
  -F "play_count_column=播放量" \
  http://localhost:5002/api/process
```

#### Python请求
```python
import requests

files = {'file': open('your_file.xlsx', 'rb')}
data = {
    'link_column': '视频链接',
    'play_count_column': '播放量',
    'sheet_name': 0
}

response = requests.post('http://localhost:5002/api/process', files=files, data=data)
result = response.json()

if result['success']:
    task_id = result['task_id']
    # 查询处理状态
    status_response = requests.get(f'http://localhost:5002/status/{task_id}')
    print(status_response.json())
```

### 返回格式
```json
{
  "success": true,
  "task_id": "uuid-string",
  "status_url": "/status/uuid-string",
  "message": "文件已上传，正在处理中..."
}
```

## 📊 状态查询

### 查询接口
```
GET /status/<task_id>
```

### 返回格式
```json
{
  "status": "processing",
  "message": "已处理 10/100 行 (10.0%)",
  "progress": 10.0,
  "processed": 10,
  "total": 100,
  "success_count": 8
}
```

### 状态说明
- `queued`: 任务已排队
- `processing`: 正在处理
- `completed`: 处理完成
- `error`: 处理失败

## 📁 文件结构

```
播放量服务/
├── 播放量_web.py          # 主服务文件
├── start_service.py       # 启动脚本
├── requirements.txt       # 依赖列表
├── templates/
│   └── index.html        # Web界面模板
├── uploads/              # 上传文件存储（自动创建）
├── downloads/            # 处理结果存储（自动创建）
└── 播放量服务_README.md  # 本文档
```

## ⚙️ 配置说明

### 端口配置
默认端口: `5002`

如需修改端口，编辑 `播放量_web.py` 文件最后一行：
```python
app.run(host='0.0.0.0', port=5002, debug=True)  # 修改port参数
```

### 线程数配置
默认使用3个线程处理。如需修改，编辑 `播放量_web.py` 中的：
```python
with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:  # 修改max_workers
```

### 安全密钥
生产环境请修改 `播放量_web.py` 中的密钥：
```python
app.secret_key = 'your-secret-key-here'  # 修改为安全的密钥
```

## 🔧 部署到服务器

### 本地部署
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动服务
python start_service.py

# 3. 访问服务
# http://localhost:5002
```

### 服务器部署
```bash
# 1. 上传文件到服务器
scp -r 播放量服务/ user@server:/path/to/app/

# 2. 在服务器上安装依赖
pip install -r requirements.txt

# 3. 启动服务（后台运行）
nohup python start_service.py > service.log 2>&1 &

# 4. 访问服务
# http://server-ip:5002
```

## 🛡️ 注意事项

### 性能建议
- **文件大小**: 建议单个Excel文件不超过10MB
- **数据量**: 建议每次处理不超过1000行数据
- **并发限制**: 建议同时处理的任务不超过3个

### 安全建议
- 生产环境请修改默认密钥
- 建议配置HTTPS访问
- 定期清理上传和下载文件夹
- 设置文件大小限制

### 错误处理
- 网络超时: 自动重试3次
- 文件格式错误: 返回详细错误信息
- 列名不存在: 自动提示可用列名

## 🐛 常见问题

### Q: 服务启动失败
**A**: 检查依赖是否安装完整，运行 `pip install -r requirements.txt`

### Q: 无法获取播放量
**A**: 可能是网络问题或目标网站限制，程序会自动重试

### Q: 处理速度慢
**A**: 可以适当增加线程数，但注意不要对目标网站造成过大压力

### Q: 文件上传失败
**A**: 检查文件格式是否为Excel格式，文件是否损坏

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受批量数据处理的便利！** 🎉 