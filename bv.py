#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BV号提取服务 - 修复版
专门从B站链接中提取BV号
支持完整链接直接提取和短链接重定向提取
API接口：POST /api/extract-bv
部署端口：5004
"""

from flask import Flask, request, jsonify, render_template_string
import requests
import re
import json
from urllib.parse import urlparse
import time

app = Flask(__name__)

# AV号转BV号的转换算法
class BVConverter:
    """BV号和AV号转换器"""

    def __init__(self):
        # 魔法字符串，用于编码
        self.magic_str = 'FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf'

        # 创建字符到索引的映射表
        self.table = {}
        for i, char in enumerate(self.magic_str):
            self.table[char] = i

        # 位置映射数组
        self.s = [0, 1, 2, 9, 7, 5, 6, 4, 8, 3, 10, 11]

        # 常量
        self.BASE = 58
        self.MAX = 1 << 51
        self.LEN = 12
        self.XOR = 23442827791579
        self.MASK = 2251799813685247

    def encode(self, av_num):
        """AV号转BV号"""
        try:
            if isinstance(av_num, str):
                av_num = int(av_num)

            # 初始化结果数组
            r = list('BV1         ')
            it = self.LEN - 1
            tmp = (av_num | self.MAX) ^ self.XOR

            while tmp != 0:
                r[self.s[it]] = self.magic_str[tmp % self.BASE]
                tmp //= self.BASE
                it -= 1

            return ''.join(r)
        except Exception as e:
            print(f"AV号转BV号失败: {e}")
            return None

    def decode(self, bv_str):
        """BV号转AV号"""
        try:
            # 统一转换为原始大小写格式进行处理
            if not bv_str.startswith('BV') and not bv_str.startswith('bv'):
                raise ValueError("BV号必须以'BV'或'bv'开头")

            if len(bv_str) != self.LEN:
                raise ValueError("BV号长度必须为12位")

            r = 0
            for i in range(3, self.LEN):
                char = bv_str[self.s[i]]
                if char not in self.table:
                    raise ValueError(f"无效字符: {char}")
                r = r * self.BASE + self.table[char]

            return (r & self.MASK) ^ self.XOR
        except Exception as e:
            print(f"BV号转AV号失败: {e}")
            return None

# 创建转换器实例
converter = BVConverter()

def av_to_bv(av_num):
    """AV号转BV号的便捷函数"""
    return converter.encode(av_num)

# HTML模板（简化版）
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BV号提取服务 API 文档</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .container { background: white; padding: 30px; border-radius: 8px; }
        h1 { color: #2c3e50; text-align: center; }
        .status { padding: 10px; background: #d4edda; color: #155724; border-radius: 5px; margin: 10px 0; }
        .endpoint { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        pre { background: #2c3e50; color: white; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 BV号提取服务 API 文档</h1>
        
        <div class="status">
            <strong>🚀 服务状态：运行中</strong><br>
            专门用于从B站链接中提取视频信息，同时返回BV号和AV号<br>
            支持BV链接、AV链接、短链接重定向，自动双向转换
        </div>

        <h2>🔌 API 接口</h2>
        
        <div class="endpoint">
            <h3>POST /api/extract-bv</h3>
            <p><strong>功能：</strong>批量提取视频信息（BV号和AV号）</p>
            <p><strong>支持：</strong>BV链接、AV链接、短链接，自动双向转换</p>
            <p><strong>Content-Type：</strong>application/json</p>
            
            <h4>📤 响应格式示例</h4>
            <pre>{
  "success": true,
  "total": 3,
  "extracted": 3,
  "failed": 0,
  "results": [
    {
      "record_id": "rec_test_1",
      "original_link": "https://www.bilibili.com/video/BV1aRKHz2Ejd/",
      "bv_number": "BV1aRKHz2Ejd",
      "av_number": 123456789,
      "av_code": "av123456789",
      "final_link": "https://www.bilibili.com/video/BV1aRKHz2Ejd/",
      "link_type": "direct",
      "source_type": "bv",
      "status": "success"
    },
    {
      "record_id": "rec_test_2",
      "original_link": "https://www.bilibili.com/video/av776316701",
      "bv_number": "BV1t14y1N77Z",
      "av_number": 776316701,
      "av_code": "av776316701",
      "final_link": "https://www.bilibili.com/video/av776316701",
      "link_type": "direct",
      "source_type": "av",
      "status": "success"
    }
  ]
}</pre>
        </div>

        <h2>🚀 使用示例</h2>
        <pre>curl -X POST http://localhost:5004/api/extract-bv \\
  -H "Content-Type: application/json" \\
  -d @your_data.json</pre>
    </div>
</body>
</html>
'''

def extract_video_info_from_url(url):
    """从URL中提取视频信息，返回BV号和AV号"""
    try:
        result = {
            'bv_number': None,
            'av_number': None,
            'source_type': None  # 'bv', 'av', 'unknown'
        }

        # 首先尝试提取BV号 - BV + 10位字符
        bv_pattern = r'BV[A-Za-z0-9]{10}'
        bv_match = re.search(bv_pattern, url)
        if bv_match:
            bv_code = bv_match.group(0)
            result['bv_number'] = bv_code
            result['source_type'] = 'bv'
            # 将BV号转换为AV号
            av_num = converter.decode(bv_code)
            if av_num:
                result['av_number'] = av_num
                print(f"从链接中提取到BV号: {bv_code}, 转换为AV号: av{av_num}")
            return result

        # 如果没有找到BV号，尝试提取AV号
        av_pattern = r'av(\d+)'
        av_match = re.search(av_pattern, url, re.IGNORECASE)
        if av_match:
            av_num = int(av_match.group(1))
            result['av_number'] = av_num
            result['source_type'] = 'av'
            print(f"从链接中提取到AV号: av{av_num}")
            # 转换AV号为BV号
            bv_code = av_to_bv(av_num)
            if bv_code:
                result['bv_number'] = bv_code
                print(f"AV号转换为BV号: av{av_num} -> {bv_code}")
            return result

        result['source_type'] = 'unknown'
        return result
    except Exception as e:
        print(f"提取视频信息失败 {url}: {str(e)}")
        return {
            'bv_number': None,
            'av_number': None,
            'source_type': 'error'
        }

def handle_short_link(url):
    """处理短链接，返回重定向后的真实链接"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.bilibili.com/',
        }
        
        print(f"处理短链接: {url}")
        # 设置较短的超时时间
        response = requests.get(url, headers=headers, timeout=5, allow_redirects=True)
        final_url = response.url
        print(f"重定向后链接: {final_url}")
        
        # 检查是否为有效的B站链接
        if 'bilibili.com' in final_url:
            return final_url
        else:
            print(f"重定向失败，不是有效的B站链接: {final_url}")
            return None
            
    except requests.exceptions.Timeout:
        print(f"短链接重定向超时: {url}")
        return None
    except Exception as e:
        print(f"短链接重定向失败 {url}: {str(e)}")
        return None

def extract_video_info_from_link(record_id, link):
    """从单个链接中提取视频信息（BV号和AV号）"""
    try:
        original_link = link
        final_link = link
        link_type = "direct"
        video_info = None

        # 首先尝试直接从原链接提取视频信息
        video_info = extract_video_info_from_url(link)

        # 如果直接提取失败，且是短链接，则尝试重定向
        if (not video_info['bv_number'] and not video_info['av_number']) and ('b23.tv' in link or 'bili.tv' in link):
            print(f"检测到短链接，尝试重定向: {link}")
            final_link = handle_short_link(link)
            if final_link:
                link_type = "redirect"
                video_info = extract_video_info_from_url(final_link)
            else:
                return {
                    'record_id': record_id,
                    'original_link': original_link,
                    'bv_number': None,
                    'av_number': None,
                    'av_code': None,
                    'final_link': original_link,
                    'link_type': 'redirect',
                    'source_type': 'unknown',
                    'status': 'redirect_failed',
                    'error': '短链接重定向失败'
                }

        if video_info['bv_number'] or video_info['av_number']:
            return {
                'record_id': record_id,
                'original_link': original_link,
                'bv_number': video_info['bv_number'],
                'av_number': video_info['av_number'],
                'av_code': f"av{video_info['av_number']}" if video_info['av_number'] else None,
                'final_link': final_link,
                'link_type': link_type,
                'source_type': video_info['source_type'],
                'status': 'success'
            }
        else:
            return {
                'record_id': record_id,
                'original_link': original_link,
                'bv_number': None,
                'av_number': None,
                'av_code': None,
                'final_link': final_link,
                'link_type': link_type,
                'source_type': video_info['source_type'],
                'status': 'no_video_info',
                'error': '未找到视频信息'
            }

    except Exception as e:
        return {
            'record_id': record_id,
            'original_link': link,
            'bv_number': None,
            'av_number': None,
            'av_code': None,
            'final_link': link,
            'link_type': 'unknown',
            'source_type': 'error',
            'status': 'error',
            'error': str(e)
        }

@app.route('/')
def index():
    """主页 - API文档"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/extract-bv', methods=['POST'])
def extract_bv():
    """批量提取BV号API"""
    try:
        raw_data = request.get_json()
        print(f"收到数据类型: {type(raw_data)}")
        
        if not raw_data:
            return jsonify({'success': False, 'error': '请求数据为空'})
        
        # 解析数据格式
        data_list = []
        
        # 处理飞书多维表格格式
        if isinstance(raw_data, list) and len(raw_data) > 0:
            # 检查是否是飞书多维表格API格式
            if isinstance(raw_data[0], dict) and 'data' in raw_data[0] and 'items' in raw_data[0]['data']:
                print("检测到飞书多维表格格式")
                for response_item in raw_data:
                    if 'data' in response_item and 'items' in response_item['data']:
                        for item in response_item['data']['items']:
                            record_id = item.get('record_id', '')
                            fields = item.get('fields', {})
                            
                            # 提取链接字段
                            link = None
                            for field_name in ['链接', 'link', 'url', '视频链接', 'video_link']:
                                if field_name in fields:
                                    field_value = fields[field_name]
                                    
                                    # 处理对象形式 {"link": "...", "text": "...", "type": "url"}
                                    if isinstance(field_value, dict):
                                        if 'text' in field_value:
                                            link = field_value['text']
                                        elif 'link' in field_value:
                                            link = field_value['link']
                                        elif 'url' in field_value:
                                            link = field_value['url']
                                    
                                    # 处理数组形式
                                    elif isinstance(field_value, list) and len(field_value) > 0:
                                        if isinstance(field_value[0], dict):
                                            if 'text' in field_value[0]:
                                                link = field_value[0]['text']
                                            elif 'link' in field_value[0]:
                                                link = field_value[0]['link']
                                        else:
                                            link = str(field_value[0])
                                    
                                    # 处理直接字符串
                                    elif isinstance(field_value, str):
                                        link = field_value
                                    
                                    if link:
                                        break
                            
                            if link:
                                data_list.append({
                                    'record_id': record_id,
                                    'link': link
                                })
        
        if not data_list:
            return jsonify({'success': False, 'error': '没有找到有效的链接数据'})
        
        print(f"解析出 {len(data_list)} 条链接数据")
        
        # 简化处理：不使用线程池，顺序处理
        results = []
        success_count = 0

        for item in data_list:
            result = extract_video_info_from_link(item['record_id'], item['link'])
            results.append(result)
            if result['status'] == 'success':
                success_count += 1
        
        response = {
            'success': True,
            'total': len(data_list),
            'extracted': success_count,
            'failed': len(data_list) - success_count,
            'results': results
        }
        
        print(f"处理完成: 总计{len(data_list)}，成功{success_count}，失败{len(data_list) - success_count}")
        return jsonify(response)
        
    except Exception as e:
        print(f"API处理失败: {str(e)}")
        return jsonify({'success': False, 'error': f'处理失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("BV号提取服务启动中...")
    print("Web界面: http://localhost:5004")
    print("API接口: http://localhost:5004/api/extract-bv")
    print("特性: 支持完整链接和短链接BV号提取")
    # 移除debug模式，避免重启问题
    app.run(host='0.0.0.0', port=5004, debug=False) 