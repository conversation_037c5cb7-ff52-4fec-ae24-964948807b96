#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
播放量获取服务 - n8n版
专为n8n工作流设计，支持JSON数据输入
"""

import os
import re
import io
import json
import time
import uuid
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

import pandas as pd
import requests
from flask import Flask, request, jsonify, send_file, render_template_string

app = Flask(__name__)

# 全局变量存储任务状态（内存中）
tasks = {}
task_lock = threading.Lock()

# HTML模板（简化版，主要用于API文档展示）
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放量获取服务 - n8n版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 40px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .api-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .method {
            background: #28a745;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            margin-right: 10px;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            white-space: pre;
        }
        .badge {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>播放量获取服务</h1>
            <p>专为n8n工作流设计 <span class="badge">JSON数据输入</span></p>
        </div>
        
        <div class="content">
            <div class="api-section">
                <div class="api-title">🚀 n8n专用API接口</div>
                <div class="endpoint">
                    <span class="method">POST</span>/api/batch-process
                </div>
                <p><strong>描述</strong>：批量处理视频链接获取播放量，专为n8n工作流设计</p>
                
                <h4>请求格式支持多种：</h4>
                <div class="code-block">// 格式1: 标准格式
{
  "data": [
    {
      "id": "行标识符(可选)",
      "link": "视频链接",
      "title": "视频标题(可选)"
    }
  ],
  "options": {
    "max_workers": 3,
    "timeout": 10
  }
}

// 格式2: 新飞书多维表格API格式 (推荐)
[
  {
    "code": 0,
    "data": {
      "items": [
        {
          "record_id": "rec25lYbdy6iNR",
          "fields": {
            "链接": {
              "link": "https://www.bilibili.com/video/BV1aRKHz2Ejd/",
              "text": "https://www.bilibili.com/video/BV1aRKHz2Ejd/",
              "type": "url"
            }
          }
        }
      ]
    }
  }
]

// 格式3: 旧飞书多维表格API格式
[
  {
    "code": 0,
    "data": {
      "items": [
        {
          "record_id": "rec25lYbdy6iNR",
          "fields": {
            "链接": [
              {
                "text": "https://www.bilibili.com/video/BV1aRKHz2Ejd/",
                "type": "text"
              }
            ]
          }
        }
      ]
    }
  }
]

// 格式4: 原有格式 (支持已有数据)
[
  {
    "record_id": "rec25lYbdy6jcz",
    "video_link": "https://www.bilibili.com/video/BV1zvK9zSEvD/",
    "bv_number": "BV1zvK9zSEvD",
    "play_count": 141,
    "status": "success"
  }
]</div>
            </div>
            
            <div class="api-section">
                <div class="api-title">📤 返回格式</div>
                <div class="code-block">{
  "success": true,
  "task_id": "任务ID",
  "total": 数据总数,
  "status_url": "/status/任务ID",
  "download_url": "/download/任务ID"
}</div>
            </div>
            
            <div class="api-section">
                <div class="api-title">🔍 任务状态查询</div>
                <div class="endpoint">
                    <span class="method">GET</span>/status/&lt;task_id&gt;
                </div>
                <div class="code-block">{
  "status": "processing",
  "progress": 65,
  "message": "正在处理第 13/20 个视频...",
  "total": 20,
  "processed": 13,
  "success_count": 10,
  "results": [
    {
      "id": "行标识符",
      "link": "视频链接",
      "play_count": 12345,
      "status": "success"
    }
  ]
}</div>
            </div>
            
            <div class="api-section">
                <div class="api-title">📥 结果下载</div>
                <div class="endpoint">
                    <span class="method">GET</span>/download/&lt;task_id&gt;
                </div>
                <p>返回包含播放量数据的Excel文件</p>
            </div>
            
            <div class="api-section">
                <div class="api-title">💡 n8n集成示例</div>
                <p>在n8n工作流中，可以使用HTTP Request节点调用此API：</p>
                <div class="code-block">1. 读取多维表格数据
2. 使用Code节点转换为所需格式
3. HTTP Request节点调用 /api/batch-process
4. 轮询状态直到完成
5. 下载结果文件或获取JSON结果</div>
            </div>
        </div>
    </div>
</body>
</html>
'''

def get_video_play_count(url):
    """获取视频播放量"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.bilibili.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        }
        
        # 处理B站短链接 (b23.tv)
        if 'b23.tv' in url:
            print(f"检测到B站短链接: {url}")
            # 获取重定向后的真实链接
            redirect_response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)
            actual_url = redirect_response.url
            print(f"重定向后的真实链接: {actual_url}")
            
            # 检查重定向后是否为有效的B站链接
            if 'bilibili.com' in actual_url:
                url = actual_url  # 使用重定向后的真实链接
            else:
                print(f"短链接重定向失败，不是有效的B站链接: {actual_url}")
                return 0
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        content = response.text
        
        # 判断是B站还是抖音
        if 'bilibili.com' in url:
            # B站播放量匹配
            bilibili_patterns = [
                r'"view":(\d+)',  # B站API数据
                r'"play":(\d+)',  # B站播放数据
                r'视频播放量\s*(\d+)',  # 页面显示文本
                r'播放量：(\d+)',
                r'"stat":\s*\{[^}]*"view":\s*(\d+)',  # JSON中的stat对象
                r'window\.__INITIAL_STATE__=.*?"view":(\d+)',  # 初始状态数据
                r'"videoData":\s*\{[^}]*"view":\s*(\d+)',  # 视频数据
            ]
            
            for pattern in bilibili_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    try:
                        return int(matches[0])
                    except ValueError:
                        continue
        
        else:
            # 抖音播放量匹配
            douyin_patterns = [
                r'"playCount":(\d+)',
                r'"play_count":(\d+)',
                r'播放量[：:]\s*(\d+(?:\.\d+)?[万千亿]?)',
                r'(\d+(?:\.\d+)?[万千亿]?)次播放',
            ]
            
            for pattern in douyin_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    count_str = matches[0]
                    if count_str.isdigit():
                        return int(count_str)
                    else:
                        # 处理带单位的数字
                        if '万' in count_str:
                            return int(float(count_str.replace('万', '')) * 10000)
                        elif '千' in count_str:
                            return int(float(count_str.replace('千', '')) * 1000)
                        elif '亿' in count_str:
                            return int(float(count_str.replace('亿', '')) * 100000000)
                        else:
                            return int(float(count_str))
        
        return 0
    except Exception as e:
        print(f"获取播放量失败 {url}: {str(e)}")
        return 0

def process_batch_data(data_list, options, task_id):
    """批量处理数据"""
    try:
        with task_lock:
            tasks[task_id]['status'] = 'processing'
            tasks[task_id]['message'] = '开始处理数据...'
        
        total = len(data_list)
        success_count = 0
        results = []
        
        # 获取配置选项
        max_workers = options.get('max_workers', 3)
        timeout = options.get('timeout', 10)
        
        with task_lock:
            tasks[task_id]['total'] = total
            tasks[task_id]['processed'] = 0
            tasks[task_id]['results'] = []
        
        def process_single_item(item):
            try:
                item_id = item.get('id', '')
                link = item.get('link', '')
                title = item.get('title', '')
                
                if not link:
                    return {
                        'id': item_id,
                        'link': link,
                        'title': title,
                        'play_count': 0,
                        'status': 'error',
                        'error': '链接为空'
                    }
                
                play_count = get_video_play_count(str(link))
                
                result = {
                    'id': item_id,
                    'link': link,
                    'title': title,
                    'play_count': play_count,
                    'status': 'success' if play_count > 0 else 'no_data'
                }
                
                with task_lock:
                    tasks[task_id]['processed'] += 1
                    processed = tasks[task_id]['processed']
                    progress = (processed / total) * 100
                    tasks[task_id]['progress'] = progress
                    tasks[task_id]['message'] = f'正在处理第 {processed}/{total} 个视频...'
                    tasks[task_id]['results'].append(result)
                
                return result
                
            except Exception as e:
                error_result = {
                    'id': item.get('id', ''),
                    'link': item.get('link', ''),
                    'title': item.get('title', ''),
                    'play_count': 0,
                    'status': 'error',
                    'error': str(e)
                }
                
                with task_lock:
                    tasks[task_id]['processed'] += 1
                    processed = tasks[task_id]['processed']
                    progress = (processed / total) * 100
                    tasks[task_id]['progress'] = progress
                    tasks[task_id]['message'] = f'正在处理第 {processed}/{total} 个视频...'
                    tasks[task_id]['results'].append(error_result)
                
                return error_result
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_single_item, item): item for item in data_list}
            
            for future in as_completed(futures):
                result = future.result()
                if result['status'] == 'success':
                    success_count += 1
        
        # 生成Excel文件
        df = pd.DataFrame(tasks[task_id]['results'])
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='播放量数据', index=False)
        output.seek(0)
        
        with task_lock:
            tasks[task_id]['status'] = 'completed'
            tasks[task_id]['progress'] = 100
            tasks[task_id]['message'] = f'处理完成！成功获取了 {success_count} 个视频的播放量'
            tasks[task_id]['success_count'] = success_count
            tasks[task_id]['result_data'] = output.getvalue()
            tasks[task_id]['filename'] = f'play_count_results_{task_id}.xlsx'
        
    except Exception as e:
        with task_lock:
            tasks[task_id]['status'] = 'error'
            tasks[task_id]['message'] = f'处理失败: {str(e)}'

@app.route('/')
def index():
    """主页 - API文档"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/batch-process', methods=['POST'])
def batch_process():
    """批量处理API - 专为n8n设计"""
    try:
        raw_data = request.get_json()
        print(f"收到原始数据类型: {type(raw_data)}")
        if isinstance(raw_data, list) and len(raw_data) > 0:
            print(f"数组长度: {len(raw_data)}, 第一个元素类型: {type(raw_data[0])}")
            if isinstance(raw_data[0], dict):
                print(f"第一个元素的键: {list(raw_data[0].keys())}")
        elif isinstance(raw_data, dict):
            print(f"字典的键: {list(raw_data.keys())}")
        
        # 处理不同的数据格式
        data_list = []
        
        # 格式1: 直接的data数组 (原格式)
        if isinstance(raw_data, dict) and 'data' in raw_data:
            data_list = raw_data['data']
        
        # 格式2: 多维表格API格式
        elif isinstance(raw_data, list) and len(raw_data) > 0:
            # 检查n8n包装格式 [{"json": {...}}]
            if isinstance(raw_data[0], dict) and 'json' in raw_data[0]:
                # 提取n8n包装的数据
                actual_data = []
                for item in raw_data:
                    if 'json' in item:
                        actual_data.append(item['json'])
                raw_data = actual_data
            
            # 检查是否是多维表格API格式
            if isinstance(raw_data[0], dict) and 'data' in raw_data[0] and 'items' in raw_data[0]['data']:
                # 解析多维表格格式
                for response_item in raw_data:
                    if 'data' in response_item and 'items' in response_item['data']:
                        for item in response_item['data']['items']:
                            record_id = item.get('record_id', '')
                            fields = item.get('fields', {})
                            
                            # 提取链接字段 (支持多种字段名和格式)
                            link = None
                            for field_name in ['链接', 'link', 'url', '视频链接', 'video_link']:
                                if field_name in fields:
                                    field_value = fields[field_name]
                                    
                                    # 格式1: 新飞书格式 - 对象形式 {"link": "...", "text": "...", "type": "url"}
                                    if isinstance(field_value, dict):
                                        if 'text' in field_value:
                                            link = field_value['text']
                                        elif 'link' in field_value:
                                            link = field_value['link']
                                        elif 'url' in field_value:
                                            link = field_value['url']
                                    
                                    # 格式2: 数组形式 [{"text": "...", "type": "text"}]
                                    elif isinstance(field_value, list) and len(field_value) > 0:
                                        if isinstance(field_value[0], dict):
                                            if 'text' in field_value[0]:
                                                link = field_value[0]['text']
                                            elif 'link' in field_value[0]:
                                                link = field_value[0]['link']
                                            elif 'url' in field_value[0]:
                                                link = field_value[0]['url']
                                        else:
                                            link = str(field_value[0])
                                    
                                    # 格式3: 直接字符串
                                    elif isinstance(field_value, str):
                                        link = field_value
                                    
                                    if link:
                                        break
                            
                            if link:
                                data_list.append({
                                    'id': record_id,
                                    'link': link,
                                    'title': ''
                                })
            
            # 检查是否是原有格式 (包含 video_link, record_id 等字段)
            elif isinstance(raw_data[0], dict) and ('video_link' in raw_data[0] or 'record_id' in raw_data[0]):
                data_list = raw_data
            else:
                # 其他普通数组格式
                data_list = raw_data
        
        # 格式3: 直接传入的数组（通过items字段）
        elif isinstance(raw_data, dict) and 'items' in raw_data:
            data_list = raw_data['items']
        
        if not data_list or len(data_list) == 0:
            return jsonify({'success': False, 'error': '没有找到有效的数据'})
        
        # 标准化数据格式
        normalized_data = []
        for i, item in enumerate(data_list):
            if isinstance(item, dict):
                # 提取链接 (支持多种字段名)
                link = (item.get('link') or 
                       item.get('url') or 
                       item.get('视频链接') or 
                       item.get('链接') or 
                       item.get('video_link'))  # 支持原有格式的 video_link 字段
                
                if not link:
                    continue
                
                # 提取ID (支持多种字段名)
                item_id = (item.get('id') or 
                          item.get('record_id') or 
                          f'item_{i+1}')
                
                # 提取标题 (支持多种字段名)
                title = (item.get('title') or 
                        item.get('标题') or 
                        item.get('文章标题') or 
                        '')
                
                normalized_item = {
                    'id': item_id,
                    'link': link,
                    'title': title
                }
                normalized_data.append(normalized_item)
        
        print(f"数据解析完成，共找到 {len(data_list)} 条原始数据")
        if data_list:
            print(f"第一条原始数据示例: {data_list[0]}")
        
        print(f"数据标准化完成，共处理 {len(normalized_data)} 条有效数据")
        if normalized_data:
            print(f"第一条标准化数据示例: {normalized_data[0]}")
        
        # 🆕 智能数据合并和去重
        # 检测是否是重复批次数据
        original_count = len(normalized_data)
        seen_links = set()
        unique_data = []
        duplicate_count = 0
        
        for item in normalized_data:
            link = item['link']
            # 标准化链接用于比较（移除参数）
            clean_link = link.split('?')[0] if '?' in link else link
            
            if clean_link not in seen_links:
                seen_links.add(clean_link)
                unique_data.append(item)
            else:
                duplicate_count += 1
        
        # 如果检测到大量重复数据，说明是批次重复
        if duplicate_count > 0:
            print(f"🔍 检测到数据重复: 原始{original_count}条 → 去重后{len(unique_data)}条 (移除{duplicate_count}条重复)")
            normalized_data = unique_data
        
        if not normalized_data:
            return jsonify({'success': False, 'error': '没有找到有效的链接数据'})
        
        print(f"最终处理数据: {len(normalized_data)} 条唯一数据")
        
        # 获取配置选项
        options = {}
        if isinstance(raw_data, dict):
            options = raw_data.get('options', {})
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 初始化任务状态
        with task_lock:
            tasks[task_id] = {
                'status': 'started',
                'progress': 0,
                'message': '任务已开始...',
                'total': len(data_list),
                'processed': 0,
                'success_count': 0,
                'results': [],
                'result_data': None,
                'filename': None,
                'created_at': datetime.now()
            }
        
        # 在后台线程中处理
        thread = threading.Thread(
            target=process_batch_data,
            args=(normalized_data, options, task_id)
        )
        thread.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'total': len(normalized_data),
            'status_url': f'/status/{task_id}',
            'download_url': f'/download/{task_id}'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/status/<task_id>')
def get_status(task_id):
    """获取任务状态"""
    with task_lock:
        if task_id not in tasks:
            return jsonify({'status': 'not_found', 'message': '任务不存在'})
        
        task = tasks[task_id].copy()
        # 不返回result_data，太大了
        if 'result_data' in task:
            del task['result_data']
        
        return jsonify(task)

@app.route('/download/<task_id>')
def download_file(task_id):
    """下载处理后的文件"""
    with task_lock:
        if task_id not in tasks:
            return "任务不存在", 404
        
        task = tasks[task_id]
        if task['status'] != 'completed' or not task['result_data']:
            return "文件不存在", 404
        
        result_data = task['result_data']
        filename = task['filename']
    
    # 创建内存文件对象
    file_obj = io.BytesIO(result_data)
    file_obj.seek(0)
    
    return send_file(
        file_obj,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/api/results/<task_id>')
def get_results_json(task_id):
    """获取JSON格式的结果数据"""
    with task_lock:
        if task_id not in tasks:
            return jsonify({'success': False, 'error': '任务不存在'})
        
        task = tasks[task_id]
        if task['status'] != 'completed':
            return jsonify({'success': False, 'error': '任务未完成'})
        
        return jsonify({
            'success': True,
            'status': task['status'],
            'total': task['total'],
            'processed': task['processed'],
            'success_count': task['success_count'],
            'results': task['results']
        })

def cleanup_old_tasks():
    """清理旧任务（定期执行）"""
    while True:
        try:
            current_time = datetime.now()
            with task_lock:
                # 删除1小时前的任务
                tasks_to_delete = []
                for task_id, task in tasks.items():
                    if (current_time - task['created_at']).seconds > 3600:
                        tasks_to_delete.append(task_id)
                
                for task_id in tasks_to_delete:
                    del tasks[task_id]
                    print(f"清理旧任务: {task_id}")
            
            time.sleep(300)  # 每5分钟清理一次
        except Exception as e:
            print(f"清理任务失败: {e}")
            time.sleep(300)

if __name__ == '__main__':
    print("播放量获取服务启动中...")
    print("Web界面: http://localhost:5003")
    print("n8n API接口: http://localhost:5003/api/batch-process")
    print("特性: n8n版 - 支持JSON数据输入")
    
    # 启动清理线程
    cleanup_thread = threading.Thread(target=cleanup_old_tasks, daemon=True)
    cleanup_thread.start()
    
    app.run(host='0.0.0.0', port=5003, debug=False) 