# 🚀 B站数据处理服务集群 - 部署指南

## 📦 Docker镜像打包完成

已成功将以下三个服务打包成一个独立的Docker镜像：

1. **BV号提取服务** (`bv.py`) - 端口 5004
2. **播放量获取服务** (`播放量获取.py`) - 端口 5003  
3. **冷启动格式化服务** (`冷启动格式化.py`) - 端口 5001

## 🎯 服务功能说明

### 1. BV号提取服务 (端口 5004)
- **功能**: 从B站链接提取BV号和AV号，支持双向转换
- **API接口**: `POST /api/extract-bv`
- **支持链接类型**:
  - BV链接: `https://www.bilibili.com/video/BV19UjJzuEbp/`
  - AV链接: `https://www.bilibili.com/video/av776316701`
  - 短链接: `https://b23.tv/mD41xuj`
- **特色功能**: 
  - 同时返回BV号和AV号
  - 自动处理短链接重定向
  - 支持飞书多维表格格式

### 2. 播放量获取服务 (端口 5003)
- **功能**: 批量获取B站视频播放量数据
- **API接口**: `POST /api/batch-process`
- **特色功能**:
  - 多线程并发处理
  - 支持大批量数据处理
  - 任务状态跟踪
  - n8n兼容格式

### 3. 冷启动格式化服务 (端口 5001)
- **功能**: Word文档转Excel格式化工具
- **Web界面**: `GET /`
- **上传接口**: `POST /upload`
- **特色功能**:
  - 支持.docx文件上传
  - 自动数据提取和格式化
  - 生成Excel文件下载

## 🔧 部署步骤

### 步骤1: 构建Docker镜像

```bash
# 方式1: 使用构建脚本 (推荐)
./build.sh

# 方式2: 手动构建
docker build --platform linux/amd64 -t bilibili-services:latest .
```

### 步骤2: 启动服务

```bash
# 方式1: 使用docker-compose (推荐)
docker-compose up -d

# 方式2: 直接运行容器
docker run -d \
  --name bilibili-services \
  -p 5001:5001 \
  -p 5003:5003 \
  -p 5004:5004 \
  bilibili-services:latest
```

### 步骤3: 验证服务

```bash
# 运行测试脚本
./test_docker_services.sh

# 或手动检查
curl http://localhost:5004/
curl http://localhost:5003/
curl http://localhost:5001/
```

## 📡 API调用示例

### BV号提取服务调用

```bash
curl -X POST http://localhost:5004/api/extract-bv \
  -H "Content-Type: application/json" \
  -d '[{
    "data": {
      "items": [{
        "record_id": "test_001",
        "fields": {
          "链接": [{
            "text": "https://www.bilibili.com/video/BV19UjJzuEbp/",
            "type": "url"
          }]
        }
      }]
    }
  }]'
```

**响应示例**:
```json
{
  "success": true,
  "total": 1,
  "extracted": 1,
  "failed": 0,
  "results": [{
    "record_id": "test_001",
    "original_link": "https://www.bilibili.com/video/BV19UjJzuEbp/",
    "bv_number": "BV19UjJzuEbp",
    "av_number": 114557381053997,
    "av_code": "av114557381053997",
    "source_type": "bv",
    "link_type": "direct",
    "status": "success"
  }]
}
```

### 播放量获取服务调用

```bash
curl -X POST http://localhost:5003/api/batch-process \
  -H "Content-Type: application/json" \
  -d '{
    "data": [{
      "链接": "https://www.bilibili.com/video/BV19UjJzuEbp/"
    }]
  }'
```

## 🌐 端口占用情况

| 端口 | 服务 | 协议 | 说明 |
|------|------|------|------|
| 5001 | 冷启动格式化服务 | HTTP | Web界面 + API |
| 5003 | 播放量获取服务 | HTTP | API接口 |
| 5004 | BV号提取服务 | HTTP | Web界面 + API |

## ✅ 镜像特性

1. **跨架构支持**: 基于x86_64架构，适用于大多数服务器
2. **网络独立**: 包含所有依赖，无需外部网络连接
3. **自动重启**: 服务异常时自动重启
4. **健康检查**: 内置健康检查机制
5. **日志管理**: 统一日志输出，便于监控

## 🔄 管理命令

```bash
# 查看容器状态
docker ps

# 查看服务日志
docker logs -f bilibili-services

# 进入容器调试
docker exec -it bilibili-services bash

# 重启服务
docker restart bilibili-services

# 停止服务
docker stop bilibili-services

# 删除容器
docker rm bilibili-services
```

## 📋 部署清单

已创建的文件：
- ✅ `Dockerfile` - Docker构建文件
- ✅ `docker-compose.yml` - Docker Compose配置
- ✅ `start_services.py` - 统一启动脚本
- ✅ `build.sh` - 构建脚本
- ✅ `test_docker_services.sh` - 测试脚本
- ✅ `requirements.txt` - Python依赖 (已更新)
- ✅ `.dockerignore` - Docker忽略文件
- ✅ `DOCKER_README.md` - Docker使用说明
- ✅ `DEPLOYMENT.md` - 部署指南

## 🎉 部署完成

Docker镜像已准备就绪，可以部署到任何支持Docker的x86服务器环境！
