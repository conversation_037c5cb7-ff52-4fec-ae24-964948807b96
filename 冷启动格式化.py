#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import tempfile
import zipfile
from datetime import datetime
from flask import Flask, request, render_template_string, send_file, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于flash消息
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制文件大小为16MB

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>冷启动数据格式化工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 20px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 30px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        #file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        
        .file-types {
            color: #999;
            font-size: 0.9em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            font-weight: 600;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            display: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 冷启动数据格式化</h1>
            <p class="subtitle">将DOCX文件转换为格式化的XLSX表格</p>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form id="upload-form" method="POST" enctype="multipart/form-data">
            <div class="upload-area" id="upload-area">
                <input type="file" id="file-input" name="file" accept=".docx" required>
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                <div class="file-types">支持格式：.docx</div>
            </div>
            
            <div class="file-info" id="file-info">
                <strong>已选择文件：</strong> <span id="file-name"></span><br>
                <strong>文件大小：</strong> <span id="file-size"></span>
            </div>
            
            <div class="progress" id="progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            
            <button type="submit" class="btn" id="submit-btn" disabled>
                ⚡ 开始转换
            </button>
        </form>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">智能解析</div>
                <div class="feature-desc">自动识别标题、链接和账号信息</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔗</div>
                <div class="feature-title">多格式支持</div>
                <div class="feature-desc">支持多种B站链接格式</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">快速转换</div>
                <div class="feature-desc">秒级完成文件格式转换</div>
            </div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const submitBtn = document.getElementById('submit-btn');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progress-bar');
        const form = document.getElementById('upload-form');

        // 文件拖拽处理
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // 文件选择处理
        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                if (!file.name.toLowerCase().endsWith('.docx')) {
                    alert('请选择.docx格式的文件！');
                    return;
                }
                
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.style.display = 'block';
                submitBtn.disabled = false;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 表单提交处理
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            if (!fileInput.files[0]) {
                alert('请先选择文件！');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            // 显示进度条
            progress.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 处理中...';

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    // 获取文件名
                    const disposition = response.headers.get('Content-Disposition');
                    let filename = 'result.xlsx';
                    if (disposition) {
                        const matches = disposition.match(/filename="([^"]+)"/);
                        if (matches) {
                            filename = matches[1];
                        }
                    }
                    
                    // 下载文件
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        // 重置界面
                        progressBar.style.width = '100%';
                        setTimeout(() => {
                            progress.style.display = 'none';
                            progressBar.style.width = '0%';
                            submitBtn.disabled = false;
                            submitBtn.textContent = '⚡ 开始转换';
                            alert('✅ 转换完成！文件已下载。');
                        }, 1000);
                    });
                } else {
                    throw new Error('转换失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ 转换失败，请检查文件格式是否正确。');
                progress.style.display = 'none';
                progressBar.style.width = '0%';
                submitBtn.disabled = false;
                submitBtn.textContent = '⚡ 开始转换';
            });
        });
    </script>
</body>
</html>
'''

def check_dependencies():
    """检查必需的依赖库"""
    try:
        import docx
        import openpyxl
        return True
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        return False

def read_docx_file(file_path):
    """读取docx文件内容"""
    try:
        from docx import Document
        
        doc = Document(file_path)
        content_lines = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:  # 只添加非空行
                content_lines.append(text)
        
        return content_lines
    except Exception as e:
        print(f"读取docx文件失败: {e}")
        return None

def parse_data_lines(lines):
    """解析数据行，复用1.py的解析逻辑"""
    
    data_records = []
    current_record = {}
    current_timestamp = ""
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 检查是否是时间戳行
        timestamp_match = re.match(r'([^:]+):\s*(\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', line)
        if timestamp_match:
            current_timestamp = timestamp_match.group(2)
            i += 1
            continue
        
        # 检查是否是序号行
        if re.match(r'^\d+\.?\s*$', line):
            # 如果已经有记录，保存它
            if current_record:
                data_records.append(current_record)
            
            # 开始新记录
            current_record = {
                'timestamp': current_timestamp,
                'serial_number': line.rstrip('.'),
                'title': '',
                'url': '',
                'metadata': ''
            }
            i += 1
            continue
        
        # 如果当前正在处理记录，收集标题、链接和备注数据
        if current_record:
            if line:  # 非空行
                # 首先尝试按原有逻辑处理单行格式
                title, url, metadata = extract_title_url_metadata(line)
                
                # 如果在单行中提取到完整信息，直接使用
                if title and url and metadata:
                    current_record['title'] = title
                    current_record['url'] = url
                    current_record['metadata'] = metadata
                # 如果只有标题和链接（在同一行），查看下一行是否有备注
                elif title and url and not metadata:
                    current_record['title'] = title
                    current_record['url'] = url
                    # 检查下一行是否有备注数据
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        if next_line and '|' in next_line and not next_line.startswith('http'):
                            current_record['metadata'] = next_line
                            i += 1  # 跳过下一行
                # 如果只有标题（多行格式），查看下两行
                elif title and not url:
                    current_record['title'] = title
                    # 查看下一行是否有链接
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        if 'https://' in next_line:
                            next_title, next_url, next_metadata = extract_title_url_metadata(next_line)
                            if next_url:
                                current_record['url'] = next_url
                                # 如果链接行没有备注，查看再下一行
                                if not next_metadata and i + 2 < len(lines):
                                    third_line = lines[i + 2].strip()
                                    if third_line and '|' in third_line and not third_line.startswith('http'):
                                        current_record['metadata'] = third_line
                                        i += 2  # 跳过下两行
                                elif next_metadata:
                                    current_record['metadata'] = next_metadata
                                    i += 1  # 跳过下一行
                # 如果只是链接行，检查下一行备注
                elif url and not title:
                    if not current_record['url']:
                        current_record['url'] = url
                    if metadata and not current_record['metadata']:
                        current_record['metadata'] = metadata
                    elif not metadata and i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        if next_line and '|' in next_line and not next_line.startswith('http'):
                            current_record['metadata'] = next_line
                            i += 1  # 跳过下一行
                # 如果只是备注行
                elif not title and not url and '|' in line and not line.startswith('http'):
                    if not current_record['metadata']:
                        current_record['metadata'] = line
        
        i += 1
    
    # 处理最后一条记录
    if current_record:
        data_records.append(current_record)
    
    return data_records

def extract_title_from_text(text):
    """从文本中提取完整标题"""
    if not text:
        return ""
    
    # 情况1: 以【开头的标题，需要找到完整的标题
    if text.startswith('【'):
        # 处理嵌套【】的情况，如【【福建招教备考】你以为的教师招聘vs实际的教师招聘？一学一个不吭气】
        if text.count('【') > 1:
            # 找最后一个】作为标题结束
            last_bracket_index = text.rfind('】')
            if last_bracket_index != -1:
                return text[1:last_bracket_index]  # 去掉开头的【和结尾的】
        else:
            # 普通的【标题】格式
            end_bracket_index = text.find('】')
            if end_bracket_index != -1:
                # 检查】后面是否还有内容
                after_bracket = text[end_bracket_index + 1:].strip()
                if after_bracket:
                    # 如果】后面还有内容，整个都是标题，但去掉开头的【
                    return text[1:]
                else:
                    # 如果】后面没有内容，正常提取【】中的内容
                    return text[1:end_bracket_index]
            else:
                # 只有开头【，没有结尾】，去掉开头的【
                return text[1:]
    
    # 情况2: 不以【开头，直接返回原文本
    return text

def extract_title_url_metadata(line):
    """从一行中提取标题、链接和备注数据"""
    title = ""
    url = ""
    metadata = ""
    
    # 更全面地匹配B站链接，支持不同参数格式
    # 格式1: ?share_source=copy_web&vd_source=[32位字符]
    # 格式2: ?smp_id_from=xxx&vd_source=[32位字符]
    bilibili_pattern1 = r'https://www\.bilibili\.com/video/BV[A-Za-z0-9]{10}/\?share_source=[^&]*&vd_source=[a-f0-9]{32}'
    bilibili_pattern2 = r'https://www\.bilibili\.com/video/BV[A-Za-z0-9]{10}/\?spm_id_from=[^&]*&vd_source=[a-f0-9A-F]{32}'
    
    # 先尝试格式1
    url_match = re.search(bilibili_pattern1, line)
    if not url_match:
        # 再尝试格式2
        url_match = re.search(bilibili_pattern2, line)
    
    if url_match:
        url = url_match.group(0)
        # 分割为链接前和链接后的部分
        before_url = line[:url_match.start()].strip()
        after_url = line[url_match.end():].strip()
        
        # 如果链接后直接跟着字符（说明是账号信息）
        if after_url:
            metadata = after_url
    else:
        # 如果没有找到标准B站链接，尝试其他格式的B站链接
        fallback_pattern = r'https://www\.bilibili\.com/video/BV[A-Za-z0-9]{10}/\?[^|\s]*'
        fallback_match = re.search(fallback_pattern, line)
        if fallback_match:
            url = fallback_match.group(0)
            before_url = line[:fallback_match.start()].strip()
            after_url = line[fallback_match.end():].strip()
            if after_url:
                metadata = after_url
        else:
            # 最后尝试匹配任何https链接
            general_url_match = re.search(r'https://[^\s\|]+', line)
            if general_url_match:
                url = general_url_match.group(0)
                before_url = line[:general_url_match.start()].strip()
                after_url = line[general_url_match.end():].strip()
                if after_url:
                    metadata = after_url
            else:
                before_url = line.strip()
                after_url = ""
    
    # 提取标题
    title = extract_title_from_text(before_url)
    
    return title, url, metadata

def parse_metadata(metadata_string):
    """解析备注数据字符串，提取各个字段"""
    if not metadata_string:
        return [""] * 8
    
    # 按|分割
    parts = metadata_string.split('|')
    
    # 确保有8个字段，不足的用空字符串填充
    while len(parts) < 8:
        parts.append("")
    
    return parts[:8]  # 只取前8个字段

def format_date(timestamp_str):
    """将时间戳格式化为2025.6.26格式"""
    if not timestamp_str:
        return ""
    
    try:
        # 解析 MM-DD HH:MM:SS 格式
        date_part = timestamp_str.split()[0]  # 获取日期部分
        month, day = date_part.split('-')
        
        # 假设年份为2025
        return f"2025.{int(month)}.{int(day)}"
    except:
        return timestamp_str

def save_to_xlsx(data_records, output_file):
    """将数据保存为Excel文件"""
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment
        
        wb = Workbook()
        ws = wb.active
        ws.title = "冷启动数据"
        
        # 设置表头
        headers = ['投放日期', '文章标题', '链接', '账号名称', 'ip地址', '文章所属', 
                  '投放人', '文章类型', '小项', '视频形式', '音频形式', '播放量']
        
        # 写入表头并设置样式
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 写入数据
        for row_idx, record in enumerate(data_records, 2):
            # 解析备注数据
            metadata_fields = parse_metadata(record['metadata'])
            
            # 构建行数据
            row_data = [
                format_date(record['timestamp']),  # 投放日期
                record['title'],                   # 文章标题
                record['url'],                     # 链接
                metadata_fields[0],                # 账号名称
                metadata_fields[1],                # ip地址
                metadata_fields[2],                # 文章所属
                metadata_fields[3],                # 投放人
                metadata_fields[4],                # 文章类型
                metadata_fields[5],                # 小项
                metadata_fields[6],                # 视频形式
                metadata_fields[7],                # 音频形式
                ""                                 # 播放量（空白）
            ]
            
            # 写入行数据
            for col, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col, value=value)
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 最大宽度50
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(output_file)
        return True
        
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    if 'file' not in request.files:
        return jsonify({'error': '没有文件被上传'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if not file.filename.lower().endswith('.docx'):
        return jsonify({'error': '文件格式不支持，请上传.docx文件'}), 400
    
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存上传的文件
            input_filename = secure_filename(file.filename)
            input_path = os.path.join(temp_dir, input_filename)
            file.save(input_path)
            
            # 读取docx文件
            lines = read_docx_file(input_path)
            if lines is None:
                return jsonify({'error': '无法读取docx文件'}), 400
            
            # 解析数据
            data_records = parse_data_lines(lines)
            if not data_records:
                return jsonify({'error': '未找到有效的数据记录'}), 400
            
            # 生成输出文件
            output_filename = os.path.splitext(input_filename)[0] + '_处理结果.xlsx'
            output_path = os.path.join(temp_dir, output_filename)
            
            if not save_to_xlsx(data_records, output_path):
                return jsonify({'error': '生成Excel文件失败'}), 500
            
            # 返回文件供下载
            return send_file(
                output_path,
                as_attachment=True,
                download_name=output_filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
    
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

if __name__ == '__main__':
    # 检查依赖
    if not check_dependencies():
        print("❌ 请先安装必需的依赖库：")
        print("pip install flask python-docx openpyxl")
        exit(1)
    
    print("=" * 60)
    print("🌐 冷启动数据格式化 Web 服务")
    print("=" * 60)
    print("🚀 服务启动中...")
    print("📡 访问地址: http://localhost:5000")
    print("🛑 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5001) 