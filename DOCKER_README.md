# 🎬 B站数据处理服务集群 - Docker版

一个集成了三个B站数据处理服务的Docker容器，支持BV号提取、播放量获取和文档格式化功能。

## 📋 服务概览

| 服务名称 | 端口 | 功能描述 | API接口 |
|---------|------|----------|---------|
| BV号提取服务 | 5004 | 提取BV号和AV号，支持短链接 | `/api/extract-bv` |
| 播放量获取服务 | 5003 | 批量获取视频播放量数据 | `/api/batch-process` |
| 冷启动格式化服务 | 5001 | Word文档转Excel格式化 | `/upload` |

## 🚀 快速开始

### 方式1: 使用构建脚本

```bash
# 1. 运行构建脚本
./build.sh

# 2. 启动服务
docker-compose up -d
```

### 方式2: 手动构建

```bash
# 1. 构建镜像
docker build --platform linux/amd64 -t bilibili-services:latest .

# 2. 启动容器
docker run -d \
  --name bilibili-services \
  -p 5001:5001 \
  -p 5003:5003 \
  -p 5004:5004 \
  bilibili-services:latest
```

## 📡 API接口详细说明

### 🎯 BV号提取服务 (端口 5004)

**功能**: 从B站链接提取BV号和AV号，支持双向转换

**接口**: `POST http://localhost:5004/api/extract-bv`

**支持的链接类型**:
- BV链接: `https://www.bilibili.com/video/BV19UjJzuEbp/`
- AV链接: `https://www.bilibili.com/video/av776316701`
- 短链接: `https://b23.tv/mD41xuj`

**请求格式** (飞书多维表格格式):
```json
[
  {
    "data": {
      "items": [
        {
          "record_id": "rec_001",
          "fields": {
            "链接": [{"text": "https://www.bilibili.com/video/BV19UjJzuEbp/", "type": "url"}]
          }
        }
      ]
    }
  }
]
```

**响应格式**:
```json
{
  "success": true,
  "total": 1,
  "extracted": 1,
  "failed": 0,
  "results": [
    {
      "record_id": "rec_001",
      "original_link": "https://www.bilibili.com/video/BV19UjJzuEbp/",
      "bv_number": "BV19UjJzuEbp",
      "av_number": 114557381053997,
      "av_code": "av114557381053997",
      "source_type": "bv",
      "link_type": "direct",
      "status": "success"
    }
  ]
}
```

**curl测试示例**:
```bash
curl -X POST http://localhost:5004/api/extract-bv \
  -H "Content-Type: application/json" \
  -d '[{"data":{"items":[{"record_id":"test","fields":{"链接":[{"text":"https://www.bilibili.com/video/BV19UjJzuEbp/","type":"url"}]}}]}}]'
```

### 📊 播放量获取服务 (端口 5003)

**功能**: 批量获取B站视频播放量数据

**接口**: `POST http://localhost:5003/api/batch-process`

**请求格式**:
```json
{
  "data": [
    {
      "链接": "https://www.bilibili.com/video/BV19UjJzuEbp/",
      "其他字段": "其他数据"
    }
  ]
}
```

**响应格式**:
```json
{
  "success": true,
  "task_id": "task_uuid",
  "total": 1,
  "processed": 1,
  "results": [
    {
      "链接": "https://www.bilibili.com/video/BV19UjJzuEbp/",
      "播放量": "123.4万",
      "状态": "成功"
    }
  ]
}
```

**curl测试示例**:
```bash
curl -X POST http://localhost:5003/api/batch-process \
  -H "Content-Type: application/json" \
  -d '{"data":[{"链接":"https://www.bilibili.com/video/BV19UjJzuEbp/"}]}'
```

### 📄 冷启动格式化服务 (端口 5001)

**功能**: Word文档转Excel格式化工具

**Web界面**: `http://localhost:5001`

**上传接口**: `POST http://localhost:5001/upload`

**支持格式**: .docx文件

## 🔧 管理命令

```bash
# 查看服务状态
docker ps

# 查看日志
docker logs -f bilibili-services

# 进入容器
docker exec -it bilibili-services bash

# 停止服务
docker stop bilibili-services

# 重启服务
docker restart bilibili-services

# 删除容器
docker rm bilibili-services

# 删除镜像
docker rmi bilibili-services:latest
```

## 📁 文件结构

```
.
├── bv.py                    # BV号提取服务
├── 播放量获取.py             # 播放量获取服务
├── 冷启动格式化.py           # 冷启动格式化服务
├── start_services.py        # 统一启动脚本
├── requirements.txt         # Python依赖
├── Dockerfile              # Docker构建文件
├── docker-compose.yml      # Docker Compose配置
├── build.sh                # 构建脚本
├── DOCKER_README.md        # Docker说明文档
├── data/                   # 数据目录
└── temp/                   # 临时文件目录
```

## 🌐 网络访问

容器启动后，可通过以下地址访问：

- **BV号提取服务**: http://localhost:5004
- **播放量获取服务**: http://localhost:5003  
- **冷启动格式化服务**: http://localhost:5001

## ⚠️ 注意事项

1. **架构兼容性**: 镜像基于 x86_64 架构构建，适用于大多数服务器环境
2. **网络独立**: 容器内包含所有依赖，无需外部网络连接即可运行
3. **数据持久化**: 使用 volumes 挂载 data 和 temp 目录
4. **健康检查**: 内置健康检查机制，自动监控服务状态
5. **自动重启**: 服务异常时会自动重启

## 🐛 故障排除

### 端口冲突
如果端口被占用，可以修改端口映射：
```bash
docker run -d --name bilibili-services \
  -p 6001:5001 -p 6003:5003 -p 6004:5004 \
  bilibili-services:latest
```

### 查看详细日志
```bash
docker logs --tail 100 -f bilibili-services
```

### 服务健康检查
```bash
# 检查BV号提取服务
curl http://localhost:5004/

# 检查播放量获取服务
curl http://localhost:5003/

# 检查冷启动格式化服务
curl http://localhost:5001/
```
