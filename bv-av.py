#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哔哩哔哩 AV号和BV号转换器 Web API
基于 https://github.com/ivanlulyf/bilibili-av-bv-converter 的算法
"""

from flask import Flask, request, jsonify
import re
import logging

app = Flask(__name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BVConverter:
    """BV号和AV号转换器"""
    
    def __init__(self):
        # 魔法字符串，用于编码
        self.magic_str = 'FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf'
        
        # 创建字符到索引的映射表
        self.table = {}
        for i, char in enumerate(self.magic_str):
            self.table[char] = i
        
        # 位置映射数组
        self.s = [0, 1, 2, 9, 7, 5, 6, 4, 8, 3, 10, 11]
        
        # 常量
        self.BASE = 58
        self.MAX = 1 << 51
        self.LEN = 12
        self.XOR = 23442827791579
        self.MASK = 2251799813685247
    
    def encode(self, av_num):
        """
        AV号转BV号
        :param av_num: AV号数字部分 (int or str)
        :return: BV号字符串
        """
        try:
            if isinstance(av_num, str):
                av_num = int(av_num)
            
            # 初始化结果数组
            r = list('BV1         ')
            it = self.LEN - 1
            tmp = (av_num | self.MAX) ^ self.XOR
            
            while tmp != 0:
                r[self.s[it]] = self.magic_str[tmp % self.BASE]
                tmp //= self.BASE
                it -= 1
            
            return ''.join(r)
        except Exception as e:
            logger.error(f"编码错误: {e}")
            raise ValueError(f"无效的AV号: {av_num}")
    
    def decode(self, bv_str):
        """
        BV号转AV号
        :param bv_str: BV号字符串
        :return: AV号数字
        """
        try:
            # 统一转换为原始大小写格式进行处理
            if not bv_str.startswith('BV') and not bv_str.startswith('bv'):
                raise ValueError("BV号必须以'BV'或'bv'开头")
            
            if len(bv_str) != self.LEN:
                raise ValueError("BV号长度必须为12位")
            
            r = 0
            for i in range(3, self.LEN):
                char = bv_str[self.s[i]]
                if char not in self.table:
                    raise ValueError(f"无效字符: {char}")
                r = r * self.BASE + self.table[char]
            
            return (r & self.MASK) ^ self.XOR
        except Exception as e:
            logger.error(f"解码错误: {e}")
            raise ValueError(f"无效的BV号: {bv_str}")

# 创建转换器实例
converter = BVConverter()

def process_batch_items(items, batch_size=100):
    """
    分批处理转换项目
    :param items: 待处理的项目列表
    :param batch_size: 批次大小，默认100
    :return: 处理结果列表
    """
    results = []
    total_items = len(items)
    
    logger.info(f"开始分批处理 {total_items} 个项目，每批 {batch_size} 个")
    
    for batch_start in range(0, total_items, batch_size):
        batch_end = min(batch_start + batch_size, total_items)
        batch_items = items[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (total_items + batch_size - 1) // batch_size
        
        logger.info(f"处理第 {batch_num}/{total_batches} 批次，项目 {batch_start+1}-{batch_end}")
        
        # 处理当前批次
        for i, input_str in enumerate(batch_items):
            global_index = batch_start + i
            try:
                input_str = str(input_str).strip()
                
                # 自动识别类型
                if input_str.upper().startswith('BV'):
                    # BV转AV
                    av_num = converter.decode(input_str)
                    results.append({
                        "index": global_index,
                        "batch": batch_num,
                        "input": input_str,
                        "input_type": "bv",
                        "output": f"av{av_num}",
                        "output_type": "av",
                        "av_num": av_num,
                        "success": True
                    })
                else:
                    # AV转BV
                    av_match = re.search(r'(\d+)', input_str)
                    if not av_match:
                        results.append({
                            "index": global_index,
                            "batch": batch_num,
                            "input": input_str,
                            "error": "无效的AV号格式",
                            "success": False
                        })
                        continue
                    
                    av_num = int(av_match.group(1))
                    bv_code = converter.encode(av_num)
                    results.append({
                        "index": global_index,
                        "batch": batch_num,
                        "input": input_str,
                        "input_type": "av",
                        "output": bv_code,
                        "output_type": "bv",
                        "av_num": av_num,
                        "success": True
                    })
            
            except Exception as e:
                results.append({
                    "index": global_index,
                    "batch": batch_num,
                    "input": input_str,
                    "error": str(e),
                    "success": False
                })
    
    return results

def process_feishu_batch_items(items, batch_size=100):
    """
    分批处理飞书格式转换项目
    :param items: 飞书格式待处理的项目列表
    :param batch_size: 批次大小，默认100
    :return: 处理结果列表
    """
    results = []
    total_items = len(items)
    
    logger.info(f"开始分批处理飞书格式 {total_items} 个项目，每批 {batch_size} 个")
    
    for batch_start in range(0, total_items, batch_size):
        batch_end = min(batch_start + batch_size, total_items)
        batch_items = items[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (total_items + batch_size - 1) // batch_size
        
        logger.info(f"处理飞书格式第 {batch_num}/{total_batches} 批次，项目 {batch_start+1}-{batch_end}")
        
        # 处理当前批次
        for i, item in enumerate(batch_items):
            global_index = batch_start + i
            try:
                record_id = item.get('record_id', '')
                if not record_id:
                    results.append({
                        "index": global_index,
                        "batch": batch_num,
                        "error": "缺少record_id字段",
                        "success": False,
                        "item": item
                    })
                    continue
                
                # 提取BV号
                fields = item.get('fields', {})
                bv_field = fields.get('bv', [])
                
                if not bv_field or not isinstance(bv_field, list) or len(bv_field) == 0:
                    results.append({
                        "index": global_index,
                        "batch": batch_num,
                        "record_id": record_id,
                        "error": "缺少或无效的bv字段",
                        "success": False
                    })
                    continue
                
                bv_text = bv_field[0].get('text', '').strip()
                if not bv_text:
                    results.append({
                        "index": global_index,
                        "batch": batch_num,
                        "record_id": record_id,
                        "error": "BV号文本为空",
                        "success": False
                    })
                    continue
                
                # 转换BV号为AV号
                av_num = converter.decode(bv_text)
                
                results.append({
                    "index": global_index,
                    "batch": batch_num,
                    "record_id": record_id,
                    "text": bv_text,
                    "av_num": av_num,
                    "success": True
                })
                
            except Exception as e:
                results.append({
                    "index": global_index,
                    "batch": batch_num,
                    "record_id": item.get('record_id', ''),
                    "text": bv_text if 'bv_text' in locals() else '',
                    "error": str(e),
                    "success": False
                })
    
    return results

@app.route('/', methods=['GET'])
def home():
    """首页API说明"""
    return jsonify({
        "service": "哔哩哔哩 AV/BV 转换器 API",
        "version": "2.0.0",
        "features": {
            "max_items": 2000,
            "auto_batch": True,
            "batch_size": 100
        },
        "endpoints": {
            "POST /convert": "转换AV号或BV号",
            "GET /av2bv/<av_num>": "AV号转BV号",
            "GET /bv2av/<bv_code>": "BV号转AV号",
            "POST /batch": "批量转换 (最多2000个，自动分批)",
            "POST /feishu-convert": "飞书多维表格格式转换 (最多2000个，自动分批)",
            "GET /health": "健康检查"
        },
        "examples": {
            "convert": {
                "method": "POST",
                "body": {
                    "input": "av170001",
                    "type": "auto"
                }
            },
            "av2bv": "GET /av2bv/170001",
            "bv2av": "GET /bv2av/BV17x411w7KC",
            "feishu_convert": {
                "method": "POST",
                "body": "飞书多维表格JSON格式，包含BV号数据"
            }
        }
    })

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "service": "bilibili-converter-api"
    })

@app.route('/convert', methods=['POST'])
def convert():
    """
    通用转换端点
    支持自动识别输入类型并进行转换
    """
    try:
        data = request.get_json()
        if not data or 'input' not in data:
            return jsonify({
                "error": "缺少必需参数 'input'",
                "code": 400
            }), 400
        
        input_str = str(data['input']).strip()
        convert_type = data.get('type', 'auto')
        
        # 自动识别输入类型
        if convert_type == 'auto':
            if input_str.upper().startswith('BV'):
                convert_type = 'bv2av'
            elif input_str.lower().startswith('av') or input_str.isdigit():
                convert_type = 'av2bv'
            else:
                return jsonify({
                    "error": "无法识别输入类型，请确保输入正确的AV号或BV号",
                    "code": 400
                }), 400
        
        if convert_type == 'av2bv':
            # AV号转BV号
            av_match = re.search(r'(\d+)', input_str)
            if not av_match:
                return jsonify({
                    "error": "无效的AV号格式",
                    "code": 400
                }), 400
            
            av_num = int(av_match.group(1))
            bv_code = converter.encode(av_num)
            
            return jsonify({
                "success": True,
                "input": input_str,
                "input_type": "av",
                "output": bv_code,
                "output_type": "bv",
                "av_num": av_num,
                "bilibili_url": f"https://www.bilibili.com/video/{bv_code}"
            })
        
        elif convert_type == 'bv2av':
            # BV号转AV号
            av_num = converter.decode(input_str)
            
            return jsonify({
                "success": True,
                "input": input_str,
                "input_type": "bv",
                "output": f"av{av_num}",
                "output_type": "av",
                "av_num": av_num,
                "bilibili_url": f"https://www.bilibili.com/video/av{av_num}"
            })
        
        else:
            return jsonify({
                "error": "无效的转换类型，支持: auto, av2bv, bv2av",
                "code": 400
            }), 400
    
    except ValueError as e:
        return jsonify({
            "error": str(e),
            "code": 400
        }), 400
    except Exception as e:
        logger.error(f"转换错误: {e}")
        return jsonify({
            "error": "内部服务器错误",
            "code": 500
        }), 500

@app.route('/av2bv/<av_input>', methods=['GET'])
def av_to_bv(av_input):
    """
    AV号转BV号 (GET请求)
    :param av_input: AV号 (可以是数字或包含av前缀)
    """
    try:
        # 提取数字部分
        av_match = re.search(r'(\d+)', av_input)
        if not av_match:
            return jsonify({
                "error": "无效的AV号格式",
                "code": 400
            }), 400
        
        av_num = int(av_match.group(1))
        bv_code = converter.encode(av_num)
        
        return jsonify({
            "success": True,
            "input": av_input,
            "av_num": av_num,
            "bv_code": bv_code,
            "bilibili_url": f"https://www.bilibili.com/video/{bv_code}"
        })
    
    except ValueError as e:
        return jsonify({
            "error": str(e),
            "code": 400
        }), 400
    except Exception as e:
        logger.error(f"AV转BV错误: {e}")
        return jsonify({
            "error": "内部服务器错误",
            "code": 500
        }), 500

@app.route('/bv2av/<bv_code>', methods=['GET'])
def bv_to_av(bv_code):
    """
    BV号转AV号 (GET请求)
    :param bv_code: BV号
    """
    try:
        av_num = converter.decode(bv_code)
        
        return jsonify({
            "success": True,
            "input": bv_code,
            "bv_code": bv_code,
            "av_num": av_num,
            "av_code": f"av{av_num}",
            "bilibili_url": f"https://www.bilibili.com/video/av{av_num}"
        })
    
    except ValueError as e:
        return jsonify({
            "error": str(e),
            "code": 400
        }), 400
    except Exception as e:
        logger.error(f"BV转AV错误: {e}")
        return jsonify({
            "error": "内部服务器错误",
            "code": 500
        }), 500

@app.route('/feishu-convert', methods=['POST'])
def feishu_convert():
    """
    飞书多维表格格式转换端点
    处理飞书API返回的JSON格式，提取BV号并转换为AV号
    支持最多2000个项目，自动分批处理
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "error": "缺少JSON数据",
                "code": 400
            }), 400
        
        # 支持两种格式：直接数组 或 包含在第一个元素的data.items中
        items = []
        if isinstance(data, list) and len(data) > 0:
            if 'data' in data[0] and 'items' in data[0]['data']:
                # 飞书API响应格式
                items = data[0]['data']['items']
                total_count = data[0]['data'].get('total', len(items))
            else:
                # 直接的items数组
                items = data
        elif isinstance(data, dict) and 'items' in data:
            # 直接包含items的对象
            items = data['items']
        else:
            return jsonify({
                "error": "无效的数据格式，期望包含items数组的飞书格式",
                "code": 400
            }), 400
        
        # 检查数量限制
        if len(items) > 2000:
            return jsonify({
                "error": f"输入数量超过上限，最多支持2000个项目，当前输入{len(items)}个",
                "code": 400
            }), 400
        
        # 分批处理
        results = process_feishu_batch_items(items, batch_size=100)
        
        # 统计结果
        success_count = sum(1 for r in results if r.get('success', False))
        
        return jsonify({
            "success": True,
            "total": len(items),
            "processed": len(items),
            "success_count": success_count,
            "failed_count": len(items) - success_count,
            "batch_info": {
                "total_batches": (len(items) + 99) // 100,
                "batch_size": 100,
                "auto_batched": len(items) > 100
            },
            "results": results
        })
    
    except Exception as e:
        logger.error(f"飞书格式转换错误: {e}")
        return jsonify({
            "error": "内部服务器错误",
            "code": 500,
            "details": str(e)
        }), 500

@app.route('/batch', methods=['POST'])
def batch_convert():
    """
    批量转换端点
    支持同时转换多个AV号或BV号，最多2000个，自动分批处理
    """
    try:
        data = request.get_json()
        if not data or 'inputs' not in data:
            return jsonify({
                "error": "缺少必需参数 'inputs' (数组)",
                "code": 400
            }), 400
        
        inputs = data['inputs']
        if not isinstance(inputs, list):
            return jsonify({
                "error": "'inputs' 必须是数组",
                "code": 400
            }), 400
        
        # 提高限制到2000个
        if len(inputs) > 2000:
            return jsonify({
                "error": f"批量处理最多支持2000个项目，当前输入{len(inputs)}个",
                "code": 400
            }), 400
        
        # 分批处理
        results = process_batch_items(inputs, batch_size=100)
        
        # 统计成功和失败数量
        success_count = sum(1 for r in results if r.get('success', False))
        
        return jsonify({
            "success": True,
            "total": len(inputs),
            "success_count": success_count,
            "failed_count": len(inputs) - success_count,
            "batch_info": {
                "total_batches": (len(inputs) + 99) // 100,
                "batch_size": 100,
                "auto_batched": len(inputs) > 100
            },
            "results": results
        })
    
    except Exception as e:
        logger.error(f"批量转换错误: {e}")
        return jsonify({
            "error": "内部服务器错误",
            "code": 500
        }), 500

if __name__ == '__main__':
    print("🎬 哔哩哔哩 AV/BV 转换器 API v2.0 启动中...")
    print("📡 API 端点:")
    print("   GET  /                - API 说明")
    print("   POST /convert         - 通用转换")
    print("   GET  /av2bv/<num>     - AV转BV")
    print("   GET  /bv2av/<code>    - BV转AV")
    print("   POST /batch           - 批量转换 (最多2000个，自动分批)")
    print("   POST /feishu-convert  - 飞书格式转换 (最多2000个，自动分批)")
    print("   GET  /health          - 健康检查")
    print("🚀 服务运行在: http://127.0.0.1:5002")
    print("📊 新特性: 支持最多2000个项目，自动分批处理(每批100个)")
    
    app.run(host='0.0.0.0', port=5002, debug=True) 