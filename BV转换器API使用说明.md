# 哔哩哔哩 AV/BV 转换器 Web API v2.0

基于 [bilibili-av-bv-converter](https://github.com/ivanlulyf/bilibili-av-bv-converter) 的算法实现的 Python Web API 版本。

## 🆕 v2.0 新特性

- ✅ **大容量处理**: 支持最多 **1000个** AV/BV号同时转换
- ✅ **自动分批**: 超过100个自动分批处理，提升性能和稳定性
- ✅ **批次追踪**: 每个结果包含批次信息，便于追踪处理进度
- ✅ **增强日志**: 详细的分批处理日志记录
- ✅ **向下兼容**: 完全兼容v1.0的所有API接口

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r bv_converter_requirements.txt
```

### 2. 启动服务
```bash
python bilibili_bv_av_converter_api.py
```

服务将在 `http://127.0.0.1:5002` 启动

### 3. 运行测试
```bash
python test_bv_converter_api.py
```

## 📡 API 端点

### 🏠 主页 - GET `/`
获取API说明和使用示例

**响应示例:**
```json
{
  "service": "哔哩哔哩 AV/BV 转换器 API",
  "version": "2.0.0",
  "features": {
    "max_items": 1000,
    "auto_batch": true,
    "batch_size": 100
  },
  "endpoints": {
    "POST /convert": "转换AV号或BV号",
    "GET /av2bv/<av_num>": "AV号转BV号",
    "GET /bv2av/<bv_code>": "BV号转AV号",
    "POST /batch": "批量转换 (最多1000个，自动分批)",
    "POST /feishu-convert": "飞书多维表格格式转换 (最多1000个，自动分批)",
    "GET /health": "健康检查"
  }
}
```

### 🔄 通用转换 - POST `/convert`
支持自动识别输入类型并进行转换

**请求体:**
```json
{
  "input": "av170001",
  "type": "auto"  // 可选: auto, av2bv, bv2av
}
```

**响应示例:**
```json
{
  "success": true,
  "input": "av170001",
  "input_type": "av",
  "output": "BV17x411w7KC",
  "output_type": "bv",
  "av_num": 170001,
  "bilibili_url": "https://www.bilibili.com/video/BV17x411w7KC"
}
```

### 📈 AV转BV - GET `/av2bv/<av_input>`
将AV号转换为BV号

**示例:**
- `/av2bv/170001`
- `/av2bv/av170001`

**响应示例:**
```json
{
  "success": true,
  "input": "170001",
  "av_num": 170001,
  "bv_code": "BV17x411w7KC",
  "bilibili_url": "https://www.bilibili.com/video/BV17x411w7KC"
}
```

### 📉 BV转AV - GET `/bv2av/<bv_code>`
将BV号转换为AV号

**示例:**
- `/bv2av/BV17x411w7KC`

**响应示例:**
```json
{
  "success": true,
  "input": "BV17x411w7KC",
  "bv_code": "BV17x411w7KC",
  "av_num": 170001,
  "av_code": "av170001",
  "bilibili_url": "https://www.bilibili.com/video/av170001"
}
```

### 📦 批量转换 - POST `/batch` 🆕 增强版
支持同时转换多个AV号或BV号（**最多1000个**，自动分批处理）

**请求体:**
```json
{
  "inputs": ["av170001", "BV17x411w7KC", "av2", "555"]
}
```

**响应示例:**
```json
{
  "success": true,
  "total": 4,
  "success_count": 4,
  "failed_count": 0,
  "batch_info": {
    "total_batches": 1,
    "batch_size": 100,
    "auto_batched": false
  },
  "results": [
    {
      "index": 0,
      "batch": 1,
      "input": "av170001",
      "input_type": "av",
      "output": "BV17x411w7KC",
      "output_type": "bv",
      "av_num": 170001,
      "success": true
    }
    // ... 其他结果
  ]
}
```

### 📊 大批量处理示例 (500+ 项目)
当处理大量数据时，系统会自动分批：

**请求超过100个项目时的响应:**
```json
{
  "success": true,
  "total": 500,
  "success_count": 498,
  "failed_count": 2,
  "batch_info": {
    "total_batches": 5,
    "batch_size": 100,
    "auto_batched": true
  },
  "results": [
    {
      "index": 0,
      "batch": 1,
      "input": "av170001",
      "output": "BV17x411w7KC",
      "success": true
    },
    // ... 第1批次结果 (index 0-99)
    {
      "index": 100,
      "batch": 2,
      "input": "av170002",
      "output": "BV17x411w7KD", 
      "success": true
    }
    // ... 第2批次结果 (index 100-199)
    // ... 依此类推
  ]
}
```

### 🗂️ 飞书格式批量转换 - POST `/feishu-convert` 🆕 增强版
支持飞书多维表格格式的大批量转换（**最多1000个**，自动分批处理）

**请求体:**
```json
{
  "inputs": [
    {
      "record_id": "rec123",
      "fields": {
        "bv": [{"text": "BV17x411w7KC", "type": "text"}]
      }
    }
    // ... 更多记录，最多1000个
  ]
}
```

**响应示例:**
```json
{
  "success": true,
  "total": 150,
  "processed": 150,
  "success_count": 148,
  "failed_count": 2,
  "batch_info": {
    "total_batches": 2,
    "batch_size": 100,
    "auto_batched": true
  },
  "results": [
    {
      "index": 0,
      "batch": 1,
      "record_id": "rec123",
      "text": "BV17x411w7KC",
      "av_num": 170001,
      "success": true
    }
    // ... 更多结果
  ]
}
```

### 🩺 健康检查 - GET `/health`
检查服务状态

**响应示例:**
```json
{
  "status": "healthy",
  "service": "bilibili-converter-api"
}
```

## 🧪 使用示例

### Python 示例
```python
import requests

# AV转BV
response = requests.get("http://127.0.0.1:5002/av2bv/170001")
result = response.json()
print(f"AV170001 -> {result['bv_code']}")

# BV转AV
response = requests.get("http://127.0.0.1:5002/bv2av/BV17x411w7KC")
result = response.json()
print(f"BV17x411w7KC -> {result['av_code']}")

# 通用转换
data = {"input": "av170001", "type": "auto"}
response = requests.post("http://127.0.0.1:5002/convert", json=data)
result = response.json()
print(f"{result['input']} -> {result['output']}")

# 大批量转换（新特性）
large_inputs = [f"av{i}" for i in range(1, 501)]  # 500个AV号
data = {"inputs": large_inputs}
response = requests.post("http://127.0.0.1:5002/batch", json=data)
result = response.json()
print(f"批量转换完成: {result['success_count']}/{result['total']} 成功")
print(f"自动分批: {result['batch_info']['total_batches']} 批次")
```

### curl 示例
```bash
# AV转BV
curl "http://127.0.0.1:5002/av2bv/170001"

# BV转AV
curl "http://127.0.0.1:5002/bv2av/BV17x411w7KC"

# 通用转换
curl -X POST "http://127.0.0.1:5002/convert" \
  -H "Content-Type: application/json" \
  -d '{"input": "av170001", "type": "auto"}'

# 大批量转换
curl -X POST "http://127.0.0.1:5002/batch" \
  -H "Content-Type: application/json" \
  -d '{"inputs": ["av170001", "BV17x411w7KC", "av2", "...更多项目..."]}'
```

## ⚙️ 性能和限制

### 处理能力
- **单次最大**: 1000个AV/BV号
- **自动分批**: 每批100个，确保稳定性
- **处理性能**: 
  - 单个转换: < 1ms
  - 100个批次: < 50ms
  - 1000个项目: < 500ms (10批次)

### 错误处理
当输入超过1000个时：
```json
{
  "error": "批量处理最多支持1000个项目，当前输入1500个",
  "code": 400
}
```

## 🔧 核心算法说明

这个API基于知乎回答中提供的算法实现：
> [如何看待 2020 年 3 月 23 日哔哩哔哩将稿件的「av 号」变更为「BV 号」？ - mcfx的回答](https://www.zhihu.com/question/381784377/answer/1099438784)

### 转换算法核心要素：
- **魔法字符串**: `FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf`
- **位置映射**: `[0, 1, 2, 9, 7, 5, 6, 4, 8, 3, 10, 11]`
- **进制**: 58进制
- **XOR值**: 23442827791579
- **掩码**: 2251799813685247

## 📝 错误处理

API提供完善的错误处理，所有错误响应都包含：
```json
{
  "error": "错误描述",
  "code": 400
}
```

常见错误类型：
- `400`: 输入参数错误（无效的AV号或BV号格式、超过数量限制）
- `500`: 内部服务器错误

## 🌟 特性

- ✅ **完整API**: 支持GET和POST请求
- ✅ **自动识别**: 自动识别输入的AV号或BV号
- ✅ **大批量处理**: 支持批量转换（**最多1000个**）
- ✅ **自动分批**: 超过100个自动分批处理，避免超时
- ✅ **批次追踪**: 每个结果包含处理批次信息
- ✅ **错误处理**: 完善的错误处理和验证
- ✅ **多格式支持**: 支持 "av170001", "170001", "BV17x411w7KC" 等格式
- ✅ **健康检查**: 提供服务状态检查端点
- ✅ **详细响应**: 包含转换结果和哔哩哔哩链接
- ✅ **性能优化**: 内存高效处理，支持大数据量

## 📈 版本更新日志

### v2.0.0 (当前版本)
- 🆕 提升批量处理上限至 **1000个**
- 🆕 新增自动分批处理功能
- 🆕 增加批次信息追踪
- 🆕 优化内存使用和处理性能
- 🆕 增强错误提示信息

### v1.1.0
- ✅ 支持飞书多维表格格式
- ✅ 批量转换最多100个
- ✅ 基础错误处理

### v1.0.0
- ✅ 基础AV/BV转换功能
- ✅ RESTful API设计

## 📄 许可证

基于原项目的 MIT 许可证

## 🔗 相关链接

- [原始JavaScript版本](https://github.com/ivanlulyf/bilibili-av-bv-converter)
- [在线体验地址](https://ivanlulyf.github.io/bilibili-av-bv-converter/)
- [算法来源](https://www.zhihu.com/question/381784377/answer/1099438784) 