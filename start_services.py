#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一启动脚本 - 启动三个服务
"""

import os
import sys
import time
import signal
import threading
import subprocess
from multiprocessing import Process

def start_service(service_name, script_path, port):
    """启动单个服务"""
    try:
        print(f"🚀 启动 {service_name} (端口 {port})...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = '/app'
        
        # 启动服务
        process = subprocess.Popen([
            sys.executable, script_path
        ], env=env, cwd='/app')
        
        print(f"✅ {service_name} 已启动 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"❌ 启动 {service_name} 失败: {e}")
        return None

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在关闭所有服务...")
    sys.exit(0)

def main():
    """主函数"""
    print("=" * 60)
    print("🎬 B站数据处理服务集群")
    print("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 服务配置
    services = [
        {
            'name': 'BV号提取服务',
            'script': 'bv.py',
            'port': 5004,
            'description': '提取BV号和AV号，支持短链接重定向'
        },
        {
            'name': '播放量获取服务',
            'script': '播放量获取.py',
            'port': 5003,
            'description': '批量获取视频播放量数据'
        },
        {
            'name': '冷启动格式化服务',
            'script': '冷启动格式化.py',
            'port': 5001,
            'description': 'Word文档转Excel格式化工具'
        }
    ]
    
    processes = []
    
    # 启动所有服务
    for service in services:
        process = start_service(
            service['name'], 
            service['script'], 
            service['port']
        )
        if process:
            processes.append((service, process))
        time.sleep(2)  # 间隔启动
    
    print("\n" + "=" * 60)
    print("🎯 服务状态总览:")
    print("=" * 60)
    
    for service, process in processes:
        status = "✅ 运行中" if process.poll() is None else "❌ 已停止"
        print(f"{service['name']:<20} | 端口: {service['port']:<4} | {status}")
        print(f"{'':20} | 功能: {service['description']}")
        print(f"{'':20} | 地址: http://localhost:{service['port']}")
        print("-" * 60)
    
    print("\n📋 API接口说明:")
    print("=" * 60)
    print("🎯 BV号提取服务 (端口 5004):")
    print("   POST http://localhost:5004/api/extract-bv")
    print("   功能: 从B站链接提取BV号和AV号")
    print("   支持: BV链接、AV链接、短链接")
    print("")
    print("📊 播放量获取服务 (端口 5003):")
    print("   POST http://localhost:5003/api/batch-process")
    print("   功能: 批量获取视频播放量数据")
    print("   支持: JSON格式数据输入")
    print("")
    print("📄 冷启动格式化服务 (端口 5001):")
    print("   GET  http://localhost:5001")
    print("   POST http://localhost:5001/upload")
    print("   功能: Word文档转Excel格式化")
    print("   支持: .docx文件上传处理")
    print("=" * 60)
    
    print("\n🔄 服务监控中... (按 Ctrl+C 停止所有服务)")
    
    try:
        # 监控服务状态
        while True:
            time.sleep(10)
            
            # 检查服务状态
            for i, (service, process) in enumerate(processes):
                if process.poll() is not None:
                    print(f"⚠️  {service['name']} 意外停止，尝试重启...")
                    new_process = start_service(
                        service['name'], 
                        service['script'], 
                        service['port']
                    )
                    if new_process:
                        processes[i] = (service, new_process)
                        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
    finally:
        print("🔄 正在停止所有服务...")
        for service, process in processes:
            try:
                if process.poll() is None:
                    process.terminate()
                    process.wait(timeout=5)
                    print(f"✅ {service['name']} 已停止")
            except Exception as e:
                print(f"⚠️  停止 {service['name']} 时出错: {e}")
        
        print("👋 所有服务已停止")

if __name__ == '__main__':
    main()
