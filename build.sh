#!/bin/bash

# B站数据处理服务 Docker 构建脚本

echo "🎬 B站数据处理服务 Docker 构建脚本"
echo "=================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查必要文件
required_files=("bv.py" "播放量获取.py" "冷启动格式化.py" "requirements.txt" "Dockerfile")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件检查通过"

# 创建必要目录
mkdir -p data temp

echo "📦 开始构建 Docker 镜像..."

# 构建镜像
docker build --platform linux/amd64 -t bilibili-services:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker 镜像构建成功!"
    
    # 显示镜像信息
    echo ""
    echo "📋 镜像信息:"
    docker images bilibili-services:latest
    
    echo ""
    echo "🚀 启动方式:"
    echo "方式1 - 使用 docker run:"
    echo "docker run -d --name bilibili-services -p 5001:5001 -p 5003:5003 -p 5004:5004 bilibili-services:latest"
    echo ""
    echo "方式2 - 使用 docker-compose:"
    echo "docker-compose up -d"
    echo ""
    echo "🔍 查看日志:"
    echo "docker logs -f bilibili-services"
    echo ""
    echo "🛑 停止服务:"
    echo "docker stop bilibili-services"
    echo "或者: docker-compose down"
    
else
    echo "❌ Docker 镜像构建失败!"
    exit 1
fi
