# 📊 播放量服务 - 多格式支持版

## 🎯 更新说明

优化了 n8n 版播放量服务，现在支持同时处理多种数据格式，包括：

1. **原有格式** - code.json 格式数据
2. **新飞书多维表格格式** - 最新的飞书API返回格式
3. **旧飞书多维表格格式** - 兼容旧版本飞书API
4. **标准格式** - 通用的数据格式

## 🔧 支持的数据格式

### 格式1: 原有格式 (code.json)
```json
[
  {
    "record_id": "rec25lYbdy6jcz",
    "video_link": "https://www.bilibili.com/video/BV1zvK9zSEvD/...",
    "bv_number": "BV1zvK9zSEvD",
    "play_count": 141,
    "status": "success"
  }
]
```

### 格式2: 新飞书多维表格格式 ⭐
```json
[
  {
    "code": 0,
    "data": {
      "has_more": false,
      "items": [
        {
          "record_id": "rec25lYbdy6iNR",
          "fields": {
            "链接": {
              "link": "https://www.bilibili.com/video/BV1aRKHz2Ejd/...",
              "text": "https://www.bilibili.com/video/BV1aRKHz2Ejd/...",
              "type": "url"
            }
          }
        }
      ],
      "total": 1
    },
    "msg": "success"
  }
]
```

### 格式3: 旧飞书多维表格格式
```json
[
  {
    "code": 0,
    "data": {
      "items": [
        {
          "record_id": "rec25lYbdy6iNR",
          "fields": {
            "链接": [
              {
                "text": "https://www.bilibili.com/video/BV1aRKHz2Ejd/...",
                "type": "text"
              }
            ]
          }
        }
      ]
    }
  }
]
```

### 格式4: 标准格式
```json
{
  "data": [
    {
      "id": "行标识符",
      "link": "视频链接",
      "title": "视频标题"
    }
  ],
  "options": {
    "max_workers": 3,
    "timeout": 10
  }
}
```

## 🚀 使用方法

### 1. 启动服务
```bash
python 播放量_n8n版.py
```
服务将在 `http://localhost:5003` 启动

### 2. API调用
```bash
# POST 请求到批量处理接口
curl -X POST http://localhost:5003/api/batch-process \
     -H "Content-Type: application/json" \
     -d @your_data.json
```

### 3. 查看结果
```bash
# 查询处理状态
curl http://localhost:5003/status/{task_id}

# 获取JSON结果
curl http://localhost:5003/api/results/{task_id}

# 下载Excel文件
curl http://localhost:5003/download/{task_id} -o results.xlsx
```

## 🔍 字段匹配规则

服务会自动识别以下字段名：

**链接字段**:
- `link`
- `url` 
- `video_link` (原有格式)
- `链接` (中文)
- `视频链接` (中文)

**ID字段**:
- `id`
- `record_id` (飞书格式)

**标题字段**:
- `title`
- `标题` (中文)
- `文章标题` (中文)

## 🧪 测试

运行测试脚本验证多格式支持：

```bash
python test_formats.py
```

测试脚本会：
1. 测试所有支持的数据格式
2. 检查任务处理状态
3. 显示处理结果

## 📝 调试信息

服务现在提供详细的调试日志：
- 数据类型识别
- 格式解析过程
- 字段提取结果
- 处理进度信息

## 🔄 n8n 集成

在 n8n 工作流中的使用步骤：

1. **读取数据** - 使用飞书节点获取多维表格数据
2. **处理数据** - 直接发送给播放量服务，无需转换
3. **获取结果** - 轮询状态接口直到完成
4. **写回数据** - 使用返回的播放量数据更新飞书表格

## ⚡ 性能优化

- 支持多线程并发处理
- 智能格式识别，减少处理开销
- 内存优化的大数据处理
- 自动清理过期任务

## 🛠️ 故障排除

如果遇到格式不兼容问题：

1. 检查控制台日志输出
2. 确认数据结构符合支持的格式
3. 使用测试脚本验证
4. 查看API文档示例

## 📞 技术支持

- 查看服务首页: `http://localhost:5003`
- 查看完整API文档和示例
- 使用测试工具验证数据格式 