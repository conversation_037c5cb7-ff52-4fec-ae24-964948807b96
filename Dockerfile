# 使用官方Python 3.11基础镜像 (x86架构)
FROM --platform=linux/amd64 python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 更换为阿里云源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -i https://mirrors.aliyun.com/pypi/simple/ -r requirements.txt

# 复制应用程序文件
COPY bv.py .
COPY 播放量获取.py .
COPY 冷启动格式化.py .
COPY start_services.py .

# 创建数据目录
RUN mkdir -p /app/data /app/temp

# 设置文件权限
RUN chmod +x start_services.py

# 暴露端口
EXPOSE 5001 5003 5004

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5004/ || exit 1

# 启动命令
CMD ["python3", "start_services.py"]
